import {
  CaptchaBoundingBox,
  PageStateResult,
  PageStateResultWithOptionalCoordinates,
} from './extract-result';
import { ElementCoordinateMapping } from '../services/coordinate-resolution';

export type BoundingRect = {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
};

export interface InteractivityOptions {
  status: 'enabled' | 'paused' | 'completed';
  cropBox: CaptchaBoundingBox;
  inputBoxRects: BoundingRect[];
}

export type AgentState = {
  status:
    | 'waiting-for-agent'
    | 'waiting-for-human'
    | 'completed'
    | 'initiated'
    | 'initial'
    | 'other'
    | 'error';
  page?: PageStateResultWithOptionalCoordinates;
  contextId?: string;
  history?: PageStateResult[];
  sessionId?: string;
  sessionConnectUrl?: string;
  workflowId?: string;
  interactivity?: InteractivityOptions;
  errorMessage?: string;
  captchaSetupComplete?: boolean;
  coordinateResolutionInProgress?: boolean;
  coordinateResolutionPromise?: Promise<void>;
  elementCoordinateMapping?: ElementCoordinateMapping;
  fieldNameToIdMapping?: Record<string, string>;
  skipBroadcast?: boolean; // Flag to skip WebSocket broadcasts for internal state updates
};

/**
 * CropBox update data interface for WebSocket messages
 */
export interface CropBoxUpdateData {
  type: 'cropbox-update';
  cropBox?: CaptchaBoundingBox;
  inputBoxRects?: BoundingRect[];
  [key: string]: unknown;
}
