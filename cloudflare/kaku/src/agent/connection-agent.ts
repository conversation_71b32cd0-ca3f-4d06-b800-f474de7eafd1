import {
  Agent,
  AgentContext,
  type Connection,
  type ConnectionContext,
  type WSMessage,
} from 'agents';
import { html } from 'hono/html';
import {
  refreshTensorFlowBoundingBox,
  startCaptchaMonitoring,
  stopCaptchaMonitoring,
} from '../browser';
import { CDP } from '../browser/simple-cdp';
import {
  CDPAttachedToTargetParams,
  CDPRuntimeBindingCalledParams,
  CDPRuntimeExceptionParams,
  CDPConsoleAPIParams,
  CDPEvent,
} from '../browser/types/cdp-events';
import {
  ErrorCollector,
  ErrorContext,
  ErrorDisplay,
  ErrorRouter,
  ErrorService,
} from '../common/error';
import { Environment } from '../common/types';
import { encryptData, getEncryptionKey } from '../common/utils';
import { templateEngine } from '../ui';
import { PlatformTypes } from '../ui/constants';
import { BrowserServiceFactory, RemoteBrowserService } from '../workflow/services';
import { LLMService } from '../llm/LLMService';
import { OpenAILLMRepository } from '../llm/OpenAILLMRepository';
import { AnthropicLLMRepository } from '../llm/AnthropicLLMRepository';
import { AgentState, ExtractResult, CropBoxUpdateData, FormSubmissionPayload } from './types';
import { BoundingRect } from './types/agent-state';
import {
  ActionWithoutCoordinates,
  ActionWithOptionalCoordinates,
  CaptchaBoundingBox,
  PageStateResultWithOptionalCoordinates,
  Action,
} from './types/extract-result';
import {
  CoordinateResolutionService,
  ElementCoordinateRequest,
} from './services/coordinate-resolution';
import { FormVisionResult } from '../form-generation/htmx-generator';
import { FormSubmissionEvent } from '../workflow/types/ConnectionsWorkflowParams';

export class Connections extends Agent<Environment, AgentState> {
  initialState: AgentState = {
    status: 'initial',
    captchaSetupComplete: false,
  };
  offererConn: Connection | null = null;
  answererConn: Connection | null = null;
  cdpClient: CDP | null = null;
  targetSessionId: string | null = null;
  private cdpErrorHandlers: (() => void) | null = null;
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );
  llmService: LLMService;
  coordinateResolutionService: CoordinateResolutionService;

  constructor(ctx: AgentContext, env: Environment) {
    super(ctx, env);
    const openAIRepository = new OpenAILLMRepository(env.OPENAI_API_KEY, env.AI_GATEWAY_OPENAI_URL);
    const anthropicRepository = new AnthropicLLMRepository(
      env.ANTHROPIC_API_KEY,
      env.AI_GATEWAY_ANTHROPIC_URL,
    );
    this.llmService = new LLMService({
      primaryRepo: openAIRepository,
      secondaryRepo: anthropicRepository,
    });
    this.coordinateResolutionService = new CoordinateResolutionService(this.llmService);
  }

  async getCoordinatesForActions(
    screenshot: string,
    actions: ActionWithoutCoordinates[],
  ): Promise<Action[]> {
    const viewport = await this.cdpClient!.Page.getLayoutMetrics(undefined, this.targetSessionId!);
    const viewportWidth = viewport.cssLayoutViewport.clientWidth;
    const viewportHeight = viewport.cssLayoutViewport.clientHeight;

    return this.coordinateResolutionService.resolveCoordinates(
      screenshot,
      actions,
      this.name.split(':').pop() as PlatformTypes,
      viewportWidth,
      viewportHeight,
    );
  }

  /**
   * New element-based coordinate resolution method
   */
  async getElementCoordinates(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    const viewport = await this.cdpClient!.Page.getLayoutMetrics(undefined, this.targetSessionId!);
    const viewportWidth = viewport.cssLayoutViewport.clientWidth;
    const viewportHeight = viewport.cssLayoutViewport.clientHeight;

    const elementRequest: ElementCoordinateRequest = {
      fields: formVisionResult.controls.fields,
      buttons: formVisionResult.controls.buttons,
    };

    const coordinateMapping = await this.coordinateResolutionService.resolveElementCoordinates(
      screenshot,
      elementRequest,
      this.name.split(':').pop() as PlatformTypes,
      viewportWidth,
      viewportHeight,
    );

    // Store the coordinate mapping in state
    this.setState({
      ...this.state,
      elementCoordinateMapping: coordinateMapping,
      coordinateResolutionInProgress: false,
      skipBroadcast: true,
    });
  }

  async onFormStateChange(screenshot: string, result: ExtractResult) {
    let newStatus: AgentState['status'] =
      result.extractedData.pageType === 'authenticated' ? 'completed' : 'waiting-for-human';

    // Convert to PageStateResultWithOptionalCoordinates for immediate display
    const pageWithoutCoordinates: PageStateResultWithOptionalCoordinates = {
      ...result.extractedData,
      coordinatesResolved: false,
    };

    const newState: AgentState = {
      ...this.state,
      status: newStatus,
      page: pageWithoutCoordinates,
      coordinateResolutionInProgress: true,
      history: this.state?.history
        ? [...this.state.history, result.extractedData]
        : [result.extractedData],
    };

    this.setState(newState);

    // Start coordinate resolution asynchronously
    if (
      result.extractedData.pageType !== 'authenticated' &&
      result.extractedData.actions.length > 0
    ) {
      // Use new element-based coordinate resolution if FormVisionResult is available
      if (result.formVisionResult) {
        const coordinatePromise = this.resolveElementCoordinatesAsync(
          screenshot,
          result.formVisionResult,
        );

        this.setState({
          ...this.state,
          coordinateResolutionPromise: coordinatePromise,
          skipBroadcast: true,
        });
      } else {
        // Fallback to old action-based coordinate resolution
        const coordinatePromise = this.resolveCoordinatesAsync(
          screenshot,
          result.extractedData.actions.filter((action) => action.type !== 'acknowledge'),
        );

        this.setState({
          ...this.state,
          coordinateResolutionPromise: coordinatePromise,
          skipBroadcast: true,
        });
      }
    }
  }

  private async resolveCoordinatesAsync(
    screenshot: string,
    actions: ActionWithoutCoordinates[],
  ): Promise<void> {
    try {
      const actionsWithCoordinates = await this.getCoordinatesForActions(screenshot, actions);

      if (this.state.page) {
        const updatedPage: PageStateResultWithOptionalCoordinates = {
          ...this.state.page,
          actions: actionsWithCoordinates,
          coordinatesResolved: true,
        };

        this.setState({
          ...this.state,
          page: updatedPage,
          coordinateResolutionInProgress: false,
          skipBroadcast: true,
        });
      }
    } catch (error) {
      console.error('Phase 2 coordinate resolution failed:', error);
      this.setState({
        ...this.state,
        coordinateResolutionInProgress: false,
        skipBroadcast: true,
      });
      throw error;
    }
  }

  /**
   * New element-based coordinate resolution method
   */
  private async resolveElementCoordinatesAsync(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    try {
      await this.getElementCoordinates(screenshot, formVisionResult);

      // Build field name to ID mapping for form submission
      const fieldNameToIdMapping: Record<string, string> = {};
      formVisionResult.controls.fields.forEach((field) => {
        if (field.name) {
          fieldNameToIdMapping[field.name] = field.id;
        }
      });

      // Mark coordinates as resolved
      if (this.state.page) {
        const updatedPage: PageStateResultWithOptionalCoordinates = {
          ...this.state.page,
          coordinatesResolved: true,
        };

        this.setState({
          ...this.state,
          page: updatedPage,
          fieldNameToIdMapping,
          coordinateResolutionInProgress: false,
          skipBroadcast: true,
        });
      }
    } catch (error) {
      console.error('Phase 2 element coordinate resolution failed:', error);
      this.setState({
        ...this.state,
        coordinateResolutionInProgress: false,
        skipBroadcast: true,
      });
      throw error;
    }
  }

  /**
   * Filter actions based on interaction type to implement selective action execution
   */
  private async filterActionsByInteractionType(
    allActions: ActionWithOptionalCoordinates[],
    interaction?: string,
    clickId?: string,
    formValues?: Record<string, string>,
  ): Promise<ActionWithOptionalCoordinates[]> {
    console.log(
      `[ACTION FILTER] Filtering ${allActions.length} actions for interaction type: ${interaction}`,
    );

    switch (interaction) {
      case 'submit':
        // For form submissions: include input fields with values + the specific submit button
        const inputActions = allActions.filter((action) => {
          if (action.type === 'fill' || action.type === 'select') {
            const hasValue =
              formValues &&
              Object.keys(formValues).some((key) => {
                // Check if this action corresponds to a form value
                return this.actionCorrespondsToFormValue(action, key, formValues);
              });
            return hasValue;
          }
          return false;
        });

        const submitAction = allActions.find(
          (action) => action.type === 'click' && action.name === clickId,
        );

        const result = [...inputActions];
        if (submitAction) {
          result.push(submitAction);
        }

        console.log(
          `[ACTION FILTER] Form submission: ${inputActions.length} input actions + ${submitAction ? 1 : 0} submit action`,
        );
        return result;

      case 'click':
        // For individual button clicks: only the specific button that was clicked
        const buttonAction = allActions.find(
          (action) => action.type === 'click' && action.name === clickId,
        );

        console.log(`[ACTION FILTER] Button click: ${buttonAction ? 1 : 0} action(s)`);
        return buttonAction ? [buttonAction] : [];

      default:
        console.error(`[ACTION FILTER] Invalid or missing interaction type: ${interaction}`);
        const userPlatform = this.name.split(':');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error(
            `Invalid interaction type: ${interaction}. Expected: form-submission, button-click, or ai-action`,
          ),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return [];
    }
  }

  /**
   * Check if an action corresponds to a form value
   */
  private actionCorrespondsToFormValue(
    action: ActionWithOptionalCoordinates,
    formKey: string,
    formValues: Record<string, string>,
  ): boolean {
    // Direct match by action name (field.id)
    if (action.name === formKey) {
      return true;
    }

    // Check using field name to ID mapping
    if (this.state.fieldNameToIdMapping) {
      const fieldId = this.state.fieldNameToIdMapping[formKey];
      if (fieldId === action.name) {
        return true;
      }
    }

    // Special handling for radio buttons: check if action name matches field-value pattern
    // e.g., formKey='confirmation_method', formValue='whatsapp'
    // should match action.name='notification-on-another-device-whatsapp'
    const formValue = formValues[formKey];
    if (formValue && action.name.endsWith(`-${formValue}`)) {
      return true;
    }

    return false;
  }

  onStateUpdate(state: AgentState | undefined, _source: Connection | 'server'): void {
    if (!state) return;

    // Skip broadcasting if skipBroadcast flag is set
    if (state.skipBroadcast) {
      // Reset the flag directly in the state object to avoid recursive setState calls
      state.skipBroadcast = false;
      return;
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;

    this.broadcast(uiUpdate as string);
  }

  arrayBufferToBase64 = (buffer: ArrayBuffer) => Buffer.from(buffer).toString('base64');

  onConnect(connection: Connection, _ctx: ConnectionContext): void {
    console.log(`Connected: to ${this.name}`, connection.id);
    // State of interactivity for the end user. Can be paused or enabled.
    if (
      this.state.status === 'waiting-for-human' &&
      this.state.interactivity?.status === 'enabled'
    ) {
      this.broadcast(
        JSON.stringify({
          type: 'interactivity-status',
          status: this.state.interactivity.status,
          cropBox: this.state.interactivity.cropBox,
          inputBoxRects: this.state.interactivity.inputBoxRects,
        }),
      );
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;
    this.broadcast(uiUpdate as string);
  }

  onClose(connection: Connection): void | Promise<void> {
    console.log(`Disconnected from ${this.name}`, connection.id);

    this.cleanupCDPErrorMonitoring();
  }

  override async onMessage(connection: Connection, message: WSMessage) {
    if (typeof message !== 'string') {
      console.error('Invalid message type received:', typeof message);
      return;
    }
    try {
      const data = JSON.parse(message);
      switch (data.type) {
        case 'agree_and_continue': {
          console.log('Received agree_and_continue');
          await this.handleFlowInitiate({
            platform: this.name.split(':').pop() as 'facebook' | 'github' | 'test',
          });
          break;
        }
        case 'cropbox-update': {
          console.log('Received cropbox-update');
          await this.handleCropBoxUpdate(data);
          break;
        }
        case 'retry': {
          console.log('Received retry');
          await this.handleRetry();
          break;
        }
        default:
          this.broadcast(JSON.stringify({ ...data }), [connection.id]);
      }
      if (data.HEADERS && data.HEADERS['HX-Request'] === 'true') {
        await this.handleInputSubmitted(connection, data);
        return;
      }
    } catch (error) {
      console.error(error);
      throw Error(`An error occurred processing ws message ${message}, ${JSON.stringify(error)}`);
    }
  }

  async deleteAll() {
    await this.ctx.storage.deleteAll();
  }

  async onAttachedToTarget({ params }: CDPEvent<CDPAttachedToTargetParams>) {
    // get session ID
    const { sessionId, targetInfo } = params;

    if (targetInfo.type === 'page') {
      this.targetSessionId = sessionId;
      await this.cdpClient!.Page.enable(undefined, sessionId);
      await this.cdpClient!.Runtime.enable(undefined, sessionId);
    }
  }

  injectMouseTracker = async () => {
    if (!this.cdpClient) {
      console.error('CDP client not initialized');
      return;
    }

    await this.cdpClient.Runtime.evaluate({
      expression: `
      (function () {
        console.log('[KAZEEL] Injecting mouse tracker');
        document.addEventListener('mousemove', (event) => {
          const marker = document.createElement('div');
          Object.assign(marker.style, {
            position: 'absolute',
            left: event.clientX + 'px',
            top: event.clientY + 'px',
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 0, 0, 0.4)',
            zIndex: '999999',
            pointerEvents: 'none',
            transform: 'translate(-50%, -50%)',
          });
          document.body.appendChild(marker);
          setTimeout(() => marker.remove(), 500);
        });
      })();
    `,
      awaitPromise: true,
    });
  };

  async handleFlowInitiate(config: { platform: PlatformTypes }) {
    if (this.state.status !== 'initial') return;
    this.setState({
      ...this.state,
      status: 'waiting-for-agent',
    });

    const userId = this.name.split(':')[0];

    try {
      // Use the browser service to create a new session
      const browserSession = await this.browserService.createSession({
        browserArgs: ['--auto-accept-this-tab-capture'],
        device: ['desktop'],
        solveCaptchas: false,
      });
      const wsEndpoint = browserSession.wsEndpoint;

      this.cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });
      this.setupCDPErrorMonitoring();

      await this.cdpClient.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });
      // Add event listener triggered when a session is attached to a target
      this.cdpClient.Target.addEventListener(
        'attachedToTarget',
        this.onAttachedToTarget.bind(this),
      );

      const instance = await this.env.CONNECTIONS_WORKFLOW.create({
        params: { platformId: config.platform, userId, sessionId: browserSession.sessionId! },
      });

      this.setState({
        ...this.state,
        workflowId: instance.id,
        sessionId: browserSession.sessionId,
      });
    } catch (error) {
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'not_initialized',
          userId: userId,
          platformId: config.platform,
        },
        this.env,
        'error',
      );
    }
  }

  async handleAndDisplayError(errorContext: ErrorContext): Promise<void> {
    const processedError = ErrorService.processErrorForUI(errorContext);

    this.setState({
      ...this.state,
      status: 'error',
      errorMessage: processedError.userMessage,
    });

    const errorUI = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${ErrorDisplay(processedError)}
    </div>`;

    this.broadcast(errorUI as string);
  }

  private async handleInputSubmitted(_: Connection, payload: FormSubmissionPayload) {
    try {
      if (this.state.status !== 'waiting-for-human') return;
      if (!this.state.workflowId) {
        const userPlatform = this.name.split(':');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('workflowID should not be null'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return;
      }
      this.setState({ ...this.state, status: 'waiting-for-agent' });
      const { HEADERS, clickId, interaction, action, actor, ...formValues } = payload;
      const workflowId = this.state.workflowId;

      // Validate required interaction metadata
      if (!clickId) {
        const userPlatform = this.name.split(':');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('Missing required field: clickId'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return;
      }
      if (!interaction) {
        const userPlatform = this.name.split(':');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('Missing required field: interaction'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return;
      }

      console.log(`[INTERACTION] Type: ${interaction}, Clicked Button: ${clickId}`);

      if (this.state.coordinateResolutionInProgress && this.state.coordinateResolutionPromise) {
        await this.state.coordinateResolutionPromise;
      }

      const allActions = this.state.page?.actions || [];

      // Filter actions based on interaction type
      const finalActions = await this.filterActionsByInteractionType(
        allActions,
        interaction,
        clickId,
        formValues,
      );

      // Use element-based coordinate mapping if available, otherwise fall back to action coordinates
      const actions = finalActions.map((action) => {
        let coordinates = action.coordinates;

        // If we have element coordinate mapping, use it to lookup coordinates by action name (field.id)
        if (this.state.elementCoordinateMapping && action.name) {
          const elementCoord = this.state.elementCoordinateMapping[action.name];

          if (elementCoord) {
            coordinates = elementCoord;
            console.log(
              `[COORDINATE MAPPING] Found coordinates for action "${action.name}":`,
              coordinates,
            );
          } else {
            console.warn(
              `[COORDINATE MAPPING] No coordinates found for action "${action.name}". Available elements:`,
              Object.keys(this.state.elementCoordinateMapping),
            );
          }
        }

        // Mark actions with missing coordinates for later error handling
        if (
          action.type !== 'acknowledge' &&
          (!coordinates || (coordinates.x === 0 && coordinates.y === 0))
        ) {
          return { ...action, coordinates, isActionWithoutCoordinates: true };
        }

        // Map form values for fill actions
        // Note: action.name is field.id, but form values come by HTML name attribute
        // Use the stored fieldNameToIdMapping to find the correct form value
        if (action.type === 'fill' && action.name) {
          let value: string | undefined;

          // Find the HTML name attribute that corresponds to this field ID
          if (this.state.fieldNameToIdMapping) {
            // Find the name that maps to this field ID
            const fieldName = Object.keys(this.state.fieldNameToIdMapping).find(
              (name) => this.state.fieldNameToIdMapping![name] === action.name,
            );

            if (fieldName) {
              value = formValues[fieldName];
              console.log(
                `[FORM MAPPING] Mapped field ID "${action.name}" to name "${fieldName}" with value:`,
                value,
              );
            }
          }

          // Fallback: try direct lookup by action name (field.id)
          if (!value) {
            value = formValues[action.name];
          }

          if (value) {
            return {
              ...action,
              coordinates,
              value,
            };
          }
        }

        return {
          ...action,
          coordinates,
        };
      });

      // Check for actions with missing coordinates and handle with ErrorService
      const actionsWithMissingCoordinates = actions.filter(
        (action) =>
          'isActionWithoutCoordinates' in action && action.isActionWithoutCoordinates === true,
      );
      if (actionsWithMissingCoordinates.length > 0) {
        const userPlatform = this.name.split(':');
        const missingActionNames = actionsWithMissingCoordinates
          .map((action) => action.name)
          .join(', ');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error(`Actions missing coordinates: ${missingActionNames}`),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return;
      }

      const encryptionKey = await getEncryptionKey();
      const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      const payloadWithCoordinates: FormSubmissionEvent = {
        payload: encrypted,
        coordinates: this.state.elementCoordinateMapping
      }
      this.state.elementCoordinateMapping
      await workflow.sendEvent({
        type: 'form-submission',
        payload: payloadWithCoordinates,
      });
    } catch (error) {
      console.error('Failed to handle form submission:', error);

      const userPlatform = this.name.split(':');
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: userPlatform[0],
          platformId: userPlatform[1],
        },
        this.env,
        'error',
      );
    }
  }

  async handleCaptchaSolvedEvent(differenceData: {
    differencePercentage: number;
    timestamp: string;
    executionContextId: number;
    source?: string;
  }) {
    try {
      console.log(
        `Captcha solved event received, source: ${differenceData.source} - ${differenceData.differencePercentage.toFixed(2)}%`,
      );
      if (!this.state.workflowId) {
        const userPlatform = this.name.split(':');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('workflowID should not be null'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: userPlatform[0],
            platformId: userPlatform[1],
          },
          this.env,
          'error',
        );
        return;
      }

      // Send captcha solved notification to workflow
      const workflowId = this.state.workflowId;
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      await workflow.sendEvent({
        type: 'captcha-solved',
        payload: differenceData,
      });
    } catch (error) {
      console.error('Failed to handle captcha solved event:', error);

      const userPlatform = this.name.split(':');
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: userPlatform[0],
          platformId: userPlatform[1],
        },
        this.env,
        'error',
      );
    }
  }

  async stopInteractivity() {
    console.log('stopping interactivity now..');
    this.setState({
      ...this.state,
      status: 'waiting-for-agent',
      interactivity: {
        ...this.state.interactivity!,
        status: 'completed',
      },
    });
    this.broadcast(JSON.stringify({ type: 'interactivity-status', status: 'completed' }));
    if (this.cdpClient) {
      await stopCaptchaMonitoring(this.cdpClient, this.targetSessionId!);
    }
  }

  async handleCropBoxUpdate(data: CropBoxUpdateData) {
    if (this.state.interactivity?.status === 'completed') {
      return;
    }
    try {
      const newCropBox = data.cropBox;
      const inputBoxRects = data.inputBoxRects;

      if (this.isCaptchaBoundingBox(newCropBox) && this.isBoundingRectArray(inputBoxRects)) {
        this.setState({
          ...this.state,
          interactivity: {
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
          },
        });
        this.broadcast(
          JSON.stringify({
            type: 'interactivity-status',
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
          }),
        );
      } else {
        throw Error(`Invalid cropbox input type: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      console.error('Failed to handle CropBoxUpdate event:', error);

      const userPlatform = this.name.split(':');
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: userPlatform[0],
          platformId: userPlatform[1],
        },
        this.env,
        'error',
      );
    }
  }

  async handleCaptchaDetected(
    executionContextId: number,
    viewport: { width: number; height: number },
  ) {
    if (!this.cdpClient) {
      console.error('Cannot handle captcha detected: cdpClient not initialized');
      return;
    }

    if (this.state.status === 'error') {
      console.log('Agent is in error state, skipping captcha detection setup');
      throw new Error('Agent is in error state - cannot proceed with captcha detection');
    }

    if (this.state.captchaSetupComplete) {
      console.log(
        'Captcha setup already complete, skipping re-initialization. Refreshing bounding box',
      );
      await refreshTensorFlowBoundingBox(
        this.cdpClient,
        executionContextId,
        viewport,
        this.targetSessionId!,
      );
      return;
    }

    console.log('Captcha detected, setting up bindings for screenshots/difference and monitoring');

    const bindingListener = async ({ params }: CDPEvent<CDPRuntimeBindingCalledParams>) => {
      if (params.name === '__captchaSolved__') {
        try {
          const payload = JSON.parse(params.payload);
          if (payload.type === 'CAPTCHA_SOLVED') {
            console.log(`Received CAPTCHA_SOLVED via Binding`);
            // Call the internal handler method
            await this.handleCaptchaSolvedEvent({
              differencePercentage: payload.differencePercentage,
              timestamp: payload.timestamp,
              executionContextId,
              source: payload.source,
            });
          } else {
            console.warn(
              'Received unexpected payload type on __captchaSolved__ binding:',
              payload.type,
            );
          }
        } catch (parseError) {
          console.error(
            'Failed to parse payload from __captchaSolved__ binding:',
            params,
            parseError,
          );
        }
        return;
      }

      console.warn(`Received unhandled binding call: ${JSON.stringify(params)}`);
    };

    try {
      await this.cdpClient.Runtime.addBinding(
        {
          name: '__captchaSolved__',
          executionContextId,
        },
        this.targetSessionId!,
      );
      console.log('Added Runtime binding for captcha solved notifications.');

      this.cdpClient.Runtime.removeEventListener('bindingCalled', bindingListener);
      this.cdpClient.Runtime.addEventListener('bindingCalled', bindingListener);
      console.log('Attached listener for Runtime.bindingCalled');
    } catch (bindingError) {
      console.error('Failed to set up one or more Runtime bindings:', bindingError);
      return;
    }

    this.setState({
      ...this.state,
      status: 'waiting-for-human',
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
      captchaSetupComplete: true,
    });

    await startCaptchaMonitoring(
      this.cdpClient,
      {
        diffThreshold: 5,
        screenshotQuality: 90,
      },
      executionContextId,
      this.targetSessionId!,
    );
  }

  private setupCDPErrorMonitoring(): void {
    if (!this.cdpClient) return;

    const userId = this.name.split(':')[0];
    const platformId = this.name.split(':')[1];

    const handleRuntimeException = (event: CDPEvent<CDPRuntimeExceptionParams>) => {
      const exceptionDetails = event.params?.exceptionDetails;

      if (!exceptionDetails.text.includes('kazeel')) return;

      console.log(`[CDP] [DO: ${this.name}] Runtime exception:`, JSON.stringify(event, null, 2));

      const errorContext = ErrorCollector.collectError(
        'cdp',
        'SCRIPT_INJECTION_FAILED',
        event,
        'error',
        {
          userId,
          platformId,
          sessionId: this.state.sessionId || 'unknown',
        },
      );

      const classifiedError = ErrorRouter.classifyError(errorContext);
      this.handleAndDisplayError(classifiedError);
    };

    const handleConsoleAPI = (event: CDPEvent<CDPConsoleAPIParams>) => {
      // Extract console API details from event.params (simple-cdp event structure)
      const consoleType = event.params?.type;
      const exceptionDetails = event.params?.exceptionDetails;

      if (consoleType === 'error' && !exceptionDetails?.text?.includes('kazeel')) return;

      if (consoleType === 'error') {
        console.log(
          `[CDP] [DO: ${this.name}] Console error detected:`,
          JSON.stringify(event, null, 2),
        );

        const errorContext = ErrorCollector.collectError(
          'cdp',
          'SCRIPT_INJECTION_FAILED',
          event,
          'error',
          {
            userId,
            platformId,
            sessionId: this.state.sessionId || 'unknown',
          },
        );

        const classifiedError = ErrorRouter.classifyError(errorContext);
        this.handleAndDisplayError(classifiedError);
      }
    };

    // Set up CDP event listeners
    this.cdpClient.Runtime.addEventListener('exceptionThrown', handleRuntimeException);
    this.cdpClient.Runtime.addEventListener('consoleAPICalled', handleConsoleAPI);

    this.cdpErrorHandlers = () => {
      if (this.cdpClient) {
        this.cdpClient.Runtime.removeEventListener('exceptionThrown', handleRuntimeException);
        this.cdpClient.Runtime.removeEventListener('consoleAPICalled', handleConsoleAPI);
      }
    };

    console.log(`[Agent] [DO: ${this.name}] CDP error monitoring enabled`);
  }

  private cleanupCDPErrorMonitoring(): void {
    console.log(`[Agent] [DO: ${this.name}] CDP error monitoring disabled`);
    if (this.cdpErrorHandlers) {
      this.cdpErrorHandlers();
      this.cdpErrorHandlers = null;
    }
  }

  private isCaptchaBoundingBox(obj: unknown): obj is CaptchaBoundingBox {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      typeof (obj as Record<string, unknown>).x === 'number' &&
      typeof (obj as Record<string, unknown>).y === 'number' &&
      typeof (obj as Record<string, unknown>).width === 'number' &&
      typeof (obj as Record<string, unknown>).height === 'number'
    );
  }

  private isBoundingRectArray(obj: unknown): obj is BoundingRect[] {
    return (
      Array.isArray(obj) &&
      obj.every((entry) => {
        return (
          typeof entry.id === 'string' &&
          typeof entry.x === 'number' &&
          typeof entry.y === 'number' &&
          typeof entry.width === 'number' &&
          typeof entry.height === 'number'
        );
      })
    );
  }

  async handleRetry(): Promise<void> {
    this.cleanupCDPErrorMonitoring();

    this.setState(this.initialState);
  }
}
